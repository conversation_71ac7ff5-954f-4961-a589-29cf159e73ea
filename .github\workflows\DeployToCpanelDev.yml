name: Publish Website to CPanel
on:
  push:
    branches:
      - dev
    paths-ignore:
      - .env
      - .htaccess
      - app/Config/App.php
      - app/Config/Database.php
      
jobs:
   web-deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v3
    
    - name: 📂 Sync files
      uses: SamKirkland/FTP-Deploy-Action@4.3.3
      with:
        server: ${{ secrets.FTP_SERVER_DEV }}
        username: ${{ secrets.FTP_USERNAME_DEV }}
        password: ${{ secrets.FTP_PASSWORD_DEV }}
